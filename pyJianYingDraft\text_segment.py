"""定义文本片段及其相关类"""

import json
import uuid
from copy import deepcopy

from typing import Dict, Tuple, Any, TYPE_CHECKING
from typing import Union, Optional, Literal

if TYPE_CHECKING:
    from .script_file import ScriptFile

from .time_util import Timerange, tim
from .segment import ClipSettings, VisualSegment
from .animation import SegmentAnimations, Text_animation

from .metadata import FontType, EffectMeta
from .metadata import TextIntro, TextOutro, TextLoopAnim

class TextStyle:
    """字体样式类"""

    size: float
    """字体大小"""

    bold: bool
    """是否加粗"""
    italic: bool
    """是否斜体"""
    underline: bool
    """是否加下划线"""

    color: Tuple[float, float, float]
    """字体颜色, RGB三元组, 取值范围为[0, 1]"""
    alpha: float
    """字体不透明度"""

    align: Literal[0, 1, 2]
    """对齐方式"""
    vertical: bool
    """是否为竖排文本"""

    letter_spacing: int
    """字符间距"""
    line_spacing: int
    """行间距"""

    auto_wrapping: bool
    """是否自动换行"""
    max_line_width: float
    """最大行宽, 取值范围为[0, 1]"""

    def __init__(self, *, size: float = 8.0, bold: bool = False, italic: bool = False, underline: bool = False,
                 color: Tuple[float, float, float] = (1.0, 1.0, 1.0), alpha: float = 1.0,
                 align: Literal[0, 1, 2] = 0, vertical: bool = False,
                 letter_spacing: int = 0, line_spacing: int = 0,
                 auto_wrapping: bool = False, max_line_width: float = 0.82):
        """
        Args:
            size (`float`, optional): 字体大小, 默认为8.0
            bold (`bool`, optional): 是否加粗, 默认为否
            italic (`bool`, optional): 是否斜体, 默认为否
            underline (`bool`, optional): 是否加下划线, 默认为否
            color (`Tuple[float, float, float]`, optional): 字体颜色, RGB三元组, 取值范围为[0, 1], 默认为白色
            alpha (`float`, optional): 字体不透明度, 取值范围[0, 1], 默认不透明
            align (`int`, optional): 对齐方式, 0: 左对齐, 1: 居中, 2: 右对齐, 默认为左对齐
            vertical (`bool`, optional): 是否为竖排文本, 默认为否
            letter_spacing (`int`, optional): 字符间距, 定义与剪映中一致, 默认为0
            line_spacing (`int`, optional): 行间距, 定义与剪映中一致, 默认为0
            auto_wrapping (`bool`, optional): 是否自动换行, 默认关闭
            max_line_width (`float`, optional): 每行最大行宽占屏幕宽度比例, 取值范围为[0, 1], 默认为0.82
        """
        self.size = size
        self.bold = bold
        self.italic = italic
        self.underline = underline

        self.color = color
        self.alpha = alpha

        self.align = align
        self.vertical = vertical

        self.letter_spacing = letter_spacing
        self.line_spacing = line_spacing

        self.auto_wrapping = auto_wrapping
        self.max_line_width = max_line_width

class TextBorder:
    """文本描边的参数"""

    alpha: float
    """描边不透明度"""
    color: Tuple[float, float, float]
    """描边颜色, RGB三元组, 取值范围为[0, 1]"""
    width: float
    """描边宽度"""

    def __init__(self, *, alpha: float = 1.0, color: Tuple[float, float, float] = (0.0, 0.0, 0.0), width: float = 40.0):
        """
        Args:
            alpha (`float`, optional): 描边不透明度, 取值范围[0, 1], 默认为1.0
            color (`Tuple[float, float, float]`, optional): 描边颜色, RGB三元组, 取值范围为[0, 1], 默认为黑色
            width (`float`, optional): 描边宽度, 与剪映中一致, 取值范围为[0, 100], 默认为40.0
        """
        self.alpha = alpha
        self.color = color
        self.width = width / 100.0 * 0.2  # 此映射可能不完全正确

    def export_json(self) -> Dict[str, Any]:
        """导出JSON数据, 放置在素材content的styles中"""
        return {
            "content": {
                "solid": {
                    "alpha": self.alpha,
                    "color": list(self.color),
                }
            },
            "width": self.width
        }

class TextBackground:
    """文本背景参数"""

    style: Literal[1, 2]
    """背景样式"""

    alpha: float
    """背景不透明度"""
    color: str
    """背景颜色, 格式为'#RRGGBB'"""
    round_radius: float
    """背景圆角半径"""
    height: float
    """背景高度"""
    width: float
    """背景宽度"""
    horizontal_offset: float
    """背景水平偏移"""
    vertical_offset: float
    """背景竖直偏移"""

    def __init__(self, *, color: str, style: Literal[1, 2] = 1, alpha: float = 1.0, round_radius: float = 0.0,
                 height: float = 0.14, width: float = 0.14,
                 horizontal_offset: float = 0.5, vertical_offset: float = 0.5):
        """
        Args:
            color (`str`): 背景颜色, 格式为'#RRGGBB'
            style (`int`, optional): 背景样式, 1和2分别对应剪映中的两种样式, 默认为1
            alpha (`float`, optional): 背景不透明度, 与剪映中一致, 取值范围[0, 1], 默认为1.0
            round_radius (`float`, optional): 背景圆角半径, 与剪映中一致, 取值范围[0, 1], 默认为0.0
            height (`float`, optional): 背景高度, 与剪映中一致, 取值范围为[0, 1], 默认为0.14
            width (`float`, optional): 背景宽度, 与剪映中一致, 取值范围为[0, 1], 默认为0.14
            horizontal_offset (`float`, optional): 背景水平偏移, 与剪映中一致, 取值范围为[0, 1], 默认为0.5
            vertical_offset (`float`, optional): 背景竖直偏移, 与剪映中一致, 取值范围为[0, 1], 默认为0.5
        """
        self.style = style

        self.alpha = alpha
        self.color = color
        self.round_radius = round_radius
        self.height = height
        self.width = width
        self.horizontal_offset = horizontal_offset * 2 - 1
        self.vertical_offset = vertical_offset * 2 - 1

    def export_json(self) -> Dict[str, Any]:
        """生成子JSON数据, 在TextSegment导出时合并到其中"""
        return {
            "background_style": self.style,
            "background_color": self.color,
            "background_alpha": self.alpha,
            "background_round_radius": self.round_radius,
            "background_height": self.height,
            "background_width": self.width,
            "background_horizontal_offset": self.horizontal_offset,
            "background_vertical_offset": self.vertical_offset,
        }

class TextBubble:
    """文本气泡素材, 与滤镜素材本质上一致"""

    global_id: str
    """气泡全局id, 由程序自动生成"""

    effect_id: str
    resource_id: str

    def __init__(self, effect_id: str, resource_id: str):
        self.global_id = uuid.uuid4().hex
        self.effect_id = effect_id
        self.resource_id = resource_id

    def export_json(self) -> Dict[str, Any]:
        return {
            "apply_target_type": 0,
            "effect_id": self.effect_id,
            "id": self.global_id,
            "resource_id": self.resource_id,
            "type": "text_shape",
            "value": 1.0,
            # 不导出path和request_id
        }

class TextEffect(TextBubble):
    """文本花字素材, 与滤镜素材本质上也一致"""

    def export_json(self) -> Dict[str, Any]:
        ret = super().export_json()
        ret["type"] = "text_effect"
        ret["source_platform"] = 1
        return ret

class TextSegment(VisualSegment):
    """文本片段类, 目前仅支持设置基本的字体样式"""

    text: str
    """文本内容"""
    font: Optional[EffectMeta]
    """字体类型"""
    style: TextStyle
    """字体样式"""

    border: Optional[TextBorder]
    """文本描边参数, None表示无描边"""
    background: Optional[TextBackground]
    """文本背景参数, None表示无背景"""

    bubble: Optional[TextBubble]
    """文本气泡效果, 在放入轨道时加入素材列表中"""
    effect: Optional[TextEffect]
    """文本花字效果, 在放入轨道时加入素材列表中, 目前仅支持一部分花字效果"""

    def __init__(self, text_content: str = None, target_timerange: Timerange = None, *,
                 font: Optional[FontType] = None,
                 style: Optional[TextStyle] = None, clip_settings: Optional[ClipSettings] = None,
                 border: Optional[TextBorder] = None, background: Optional[TextBackground] = None,
                 text_id: Optional[str] = None, script: Optional["ScriptFile"] = None):
        """构建文本片段

        支持两种模式：
        1. 新建模式：TextSegment(text_content, target_timerange, ...)
        2. ID加载模式：TextSegment(text_id=existing_id, script=script_obj)

        Args:
            text_content (`str`, optional): 文本内容
            target_timerange (`Timerange`, optional): 片段在轨道上的时间范围
            font (`FontType`, optional): 字体类型, 默认为系统字体
            style (`TextStyle`, optional): 字体样式, 包含大小/颜色/对齐/透明度等
            clip_settings (`ClipSettings`, optional): 图像调节设置, 默认不做任何变换
            border (`TextBorder`, optional): 文本描边参数, 默认无描边
            background (`TextBackground`, optional): 文本背景参数, 默认无背景
            text_id (`str`, optional): 现有片段的ID（ID加载模式）
            script (`ScriptFile`, optional): ScriptFile实例（ID加载模式）

        Raises:
            `ValueError`: 参数错误
        """
        if text_id is not None:
            # ID加载模式：从现有片段完全复制所有数据
            if script is None:
                raise ValueError("ID加载模式下必须提供script参数")
            self._load_from_existing(text_id, script)
        else:
            # 新建模式：原有的构造逻辑
            if text_content is None or target_timerange is None:
                raise ValueError("新建模式下必须提供text_content和target_timerange参数")
            self._init_new_segment(text_content, target_timerange, font, style, clip_settings, border, background)

    def _init_new_segment(self, text_content: str, target_timerange: Timerange,
                         font: Optional[FontType], style: Optional[TextStyle],
                         clip_settings: Optional[ClipSettings], border: Optional[TextBorder],
                         background: Optional[TextBackground]):
        """新建模式的初始化逻辑"""
        super().__init__(uuid.uuid4().hex, None, target_timerange, 1.0, 1.0, False, clip_settings=clip_settings)

        self.text = text_content
        self.font = font.value if font else None
        self.style = style or TextStyle()
        self.border = border
        self.background = background

        self.bubble = None
        self.effect = None

    def _load_from_existing(self, text_id: str, script: "ScriptFile"):
        """ID加载模式：从现有片段完全复制所有数据"""
        # 查找现有片段
        existing_segment = script.get_segment_by_id(text_id)
        if existing_segment is None:
            raise ValueError(f"未找到ID为{text_id}的片段")

        # 完全复制现有片段的所有属性，确保两个实例完全相同
        if hasattr(existing_segment, 'segment_id'):
            # 普通片段：直接复制所有属性
            self._copy_from_normal_segment(existing_segment)
        else:
            # 导入片段：从raw_data重建
            self._copy_from_imported_segment(existing_segment, script)

    def _copy_from_normal_segment(self, existing_segment):
        """从普通片段复制所有数据 - 直接引用以确保修改能同步"""
        # 复制基础属性
        self.segment_id = existing_segment.segment_id
        self.material_id = existing_segment.material_id
        self.target_timerange = existing_segment.target_timerange
        self.source_timerange = existing_segment.source_timerange
        self.speed = existing_segment.speed
        self.volume = existing_segment.volume
        self.change_pitch = existing_segment.change_pitch

        # 复制图像调节设置
        if hasattr(existing_segment, 'clip_settings'):
            self.clip_settings = existing_segment.clip_settings

        # 直接引用文本相关属性，确保修改能同步
        self.text = getattr(existing_segment, 'text', '')
        self.font = getattr(existing_segment, 'font', None)
        self.style = getattr(existing_segment, 'style', TextStyle())
        self.border = getattr(existing_segment, 'border', None)
        self.background = getattr(existing_segment, 'background', None)

        # 直接引用文本效果，确保修改能同步
        self.bubble = getattr(existing_segment, 'bubble', None)
        self.effect = getattr(existing_segment, 'effect', None)

        # 直接引用动画实例
        if hasattr(existing_segment, 'animations_instance'):
            self.animations_instance = existing_segment.animations_instance
        else:
            self.animations_instance = None

        # 直接引用额外素材引用列表
        if hasattr(existing_segment, 'extra_material_refs'):
            self.extra_material_refs = existing_segment.extra_material_refs
        else:
            self.extra_material_refs = []

        # 直接引用关键帧
        if hasattr(existing_segment, 'common_keyframes'):
            self.common_keyframes = existing_segment.common_keyframes
        else:
            self.common_keyframes = []

        # 保存对原始片段的引用，用于属性同步
        self._original_segment = existing_segment

    def __setattr__(self, name, value):
        """重写属性设置，确保关键属性的修改能同步到原始片段和materials"""
        # 先设置当前实例的属性
        super().__setattr__(name, value)

        # 如果是通过ID加载的实例，同步关键属性
        if hasattr(self, '_script_ref') and self._script_ref is not None:
            # 对于普通片段，同步到原始片段
            if hasattr(self, '_original_segment') and self._original_segment is not None:
                if name in ['text', 'font', 'style', 'border', 'background', 'bubble', 'effect']:
                    setattr(self._original_segment, name, value)
                elif name in ['animations_instance', 'extra_material_refs', 'common_keyframes']:
                    setattr(self._original_segment, name, value)

            # 对于text属性，总是同步到materials（无论是普通片段还是导入片段）
            if name == 'text':
                self._sync_text_to_materials(value)

    def _sync_text_to_materials(self, new_text):
        """将文本内容同步到materials中"""
        import json

        if not hasattr(self, '_script_ref') or not hasattr(self, 'material_id'):
            return

        # 在imported_materials的texts中查找并更新
        for text_material in self._script_ref.imported_materials.get('texts', []):
            if text_material.get('id') == self.material_id:
                try:
                    # 解析现有的content
                    content_json = json.loads(text_material.get('content', '{}'))
                    # 更新text字段
                    content_json['text'] = new_text
                    # 保存回去
                    text_material['content'] = json.dumps(content_json, ensure_ascii=False)
                    break
                except (json.JSONDecodeError, Exception) as e:
                    print(f"同步文本到materials时出错: {e}")

    def _copy_from_imported_segment(self, existing_segment, script):
        """从导入片段重建所有数据"""
        # 从raw_data获取基础信息
        raw_data = existing_segment.raw_data
        self.segment_id = raw_data.get('id')
        self.material_id = getattr(existing_segment, 'material_id', None)
        self.target_timerange = existing_segment.target_timerange

        # 设置基础属性
        self.source_timerange = None  # 文本片段通常没有source_timerange
        self.speed = 1.0
        self.volume = 1.0
        self.change_pitch = False

        # 从materials中重建文本内容
        self.text = self._extract_text_content_from_materials(script)

        # 初始化文本样式和效果
        self.font = None
        self.style = TextStyle()
        self.border = None
        self.background = None
        self.bubble = None
        self.effect = None
        self.animations_instance = None

        # 复制额外素材引用（深拷贝确保独立性）
        self.extra_material_refs = deepcopy(raw_data.get('extra_material_refs', []))

        # 初始化关键帧列表
        self.common_keyframes = []

        # 重建文本效果
        self._rebuild_text_effects_from_materials(script)

        # 保存script引用，用于同步
        self._script_ref = script
        # 对于导入片段，我们没有_original_segment，但仍需要同步功能
        self._original_segment = None

    def _extract_text_content_from_materials(self, script):
        """从materials中提取文本内容"""
        import json

        # 在imported_materials的texts中查找
        for text_material in script.imported_materials.get('texts', []):
            if text_material.get('id') == self.material_id:
                try:
                    # 解析content字段中的JSON
                    content_json = json.loads(text_material.get('content', '{}'))
                    return content_json.get('text', '')
                except (json.JSONDecodeError, Exception):
                    pass

        return ''

    def _rebuild_text_effects_from_materials(self, script):
        """从materials中重建所有文本效果"""
        # 重建动画
        for animation_data in script.imported_materials.get('material_animations', []):
            if animation_data.get('id') in self.extra_material_refs:
                # 重建动画实例
                from .animation import SegmentAnimations
                self.animations_instance = SegmentAnimations()
                self.animations_instance.animation_id = animation_data.get('id')
                break

        # 重建文本气泡效果
        for bubble_data in script.imported_materials.get('text_bubbles', []):
            if bubble_data.get('id') in self.extra_material_refs:
                try:
                    effect_id = bubble_data.get('effect_id', '')
                    resource_id = bubble_data.get('resource_id', '')
                    bubble_inst = TextBubble(effect_id, resource_id)
                    bubble_inst.global_id = bubble_data.get('id')
                    self.bubble = bubble_inst
                    break
                except Exception as e:
                    print(f"重建文本气泡时出错: {e}")
                    pass

        # 重建文本花字效果
        for effect_data in script.imported_materials.get('text_effects', []):
            if effect_data.get('id') in self.extra_material_refs:
                try:
                    effect_id = effect_data.get('effect_id', '')
                    effect_inst = TextEffect(effect_id, effect_id)
                    effect_inst.global_id = effect_data.get('id')
                    self.effect = effect_inst
                    break
                except Exception as e:
                    print(f"重建文本花字效果时出错: {e}")
                    pass

    @classmethod
    def create_from_template(cls, text: str, timerange: Timerange, template: "TextSegment") -> "TextSegment":
        """根据模板创建新的文本片段, 并指定其文本内容"""
        new_segment = cls(text, timerange, style=deepcopy(template.style), clip_settings=deepcopy(template.clip_settings),
                          border=deepcopy(template.border), background=deepcopy(template.background))
        new_segment.font = deepcopy(template.font)

        # 处理动画等
        if template.animations_instance:
            new_segment.animations_instance = deepcopy(template.animations_instance)
            new_segment.animations_instance.animation_id = uuid.uuid4().hex
            new_segment.extra_material_refs.append(new_segment.animations_instance.animation_id)
        if template.bubble:
            new_segment.add_bubble(template.bubble.effect_id, template.bubble.resource_id)
        if template.effect:
            new_segment.add_effect(template.effect.effect_id)

        return new_segment

    def add_animation(self, animation_type: Union[TextIntro, TextOutro, TextLoopAnim],
                      duration: Union[str, float, None] = None) -> "TextSegment":
        """将给定的入场/出场/循环动画添加到此片段的动画列表中, 出入场动画的持续时间可以自行设置, 循环动画则会自动填满其余无动画部分

        注意: 若希望同时使用循环动画和入出场动画, 请**先添加出入场动画再添加循环动画**

        Args:
            animation_type (`TextIntro`, `TextOutro` or `TextLoopAnim`): 文本动画类型.
            duration (`str` or `float`, optional): 动画持续时间, 单位为微秒, 仅对入场/出场动画有效.
                若传入字符串则会调用`tim()`函数进行解析. 默认使用动画的时长
        """
        if duration is None:
            duration = animation_type.value.duration
        duration = min(tim(duration), self.target_timerange.duration)

        if isinstance(animation_type, TextIntro):
            start = 0
        elif isinstance(animation_type, TextOutro):
            start = self.target_timerange.duration - duration
        elif isinstance(animation_type, TextLoopAnim):
            intro_trange = self.animations_instance and self.animations_instance.get_animation_trange("in")
            outro_trange = self.animations_instance and self.animations_instance.get_animation_trange("out")
            start = intro_trange.start if intro_trange else 0
            duration = self.target_timerange.duration - start - (outro_trange.duration if outro_trange else 0)
        else:
            raise TypeError("Invalid animation type %s" % type(animation_type))

        if self.animations_instance is None:
            self.animations_instance = SegmentAnimations()
            self.extra_material_refs.append(self.animations_instance.animation_id)

        self.animations_instance.add_animation(Text_animation(animation_type, start, duration))

        return self

    def add_bubble(self, effect_id: str, resource_id: str) -> "TextSegment":
        """根据素材信息添加气泡效果, 相应素材信息可通过`ScriptFile.inspect_material`从模板中获取

        Args:
            effect_id (`str`): 气泡效果的effect_id
            resource_id (`str`): 气泡效果的resource_id
        """
        self.bubble = TextBubble(effect_id, resource_id)
        self.extra_material_refs.append(self.bubble.global_id)
        return self

    def add_effect(self, effect_id: str) -> "TextSegment":
        """根据素材信息添加花字效果, 相应素材信息可通过`ScriptFile.inspect_material`从模板中获取

        Args:
            effect_id (`str`): 花字效果的effect_id, 也同时是其resource_id
        """
        self.effect = TextEffect(effect_id, effect_id)
        self.extra_material_refs.append(self.effect.global_id)
        return self

    def export_material(self) -> Dict[str, Any]:
        """与此文本片段联系的素材, 以此不再单独定义Text_material类"""
        # 叠加各类效果的flag
        check_flag: int = 7
        if self.border:
            check_flag |= 8
        if self.background:
            check_flag |= 16

        content_json = {
            "styles": [
                {
                    "fill": {
                        "alpha": 1.0,
                        "content": {
                            "render_type": "solid",
                            "solid": {
                                "alpha": 1.0,
                                "color": list(self.style.color)
                            }
                        }
                    },
                    "range": [0, len(self.text)],
                    "size": self.style.size,
                    "bold": self.style.bold,
                    "italic": self.style.italic,
                    "underline": self.style.underline,
                    "strokes": [self.border.export_json()] if self.border else []
                }
            ],
            "text": self.text
        }
        if self.font:
            content_json["styles"][0]["font"] = {
                "id": self.font.resource_id,
                "path": "C:/%s.ttf" % self.font.name  # 并不会真正在此处放置字体文件
            }
        if self.effect:
            content_json["styles"][0]["effectStyle"] = {
                "id": self.effect.effect_id,
                "path": "C:"  # 并不会真正在此处放置素材文件
            }

        ret = {
            "id": self.material_id,
            "content": json.dumps(content_json, ensure_ascii=False),

            "typesetting": int(self.style.vertical),
            "alignment": self.style.align,
            "letter_spacing": self.style.letter_spacing * 0.05,
            "line_spacing": 0.02 + self.style.line_spacing * 0.05,

            "line_feed": 1,
            "line_max_width": self.style.max_line_width,
            "force_apply_line_max_width": False,

            "check_flag": check_flag,

            "type": "subtitle" if self.style.auto_wrapping else "text",

            # 混合 (+4)
            "global_alpha": self.style.alpha,

            # 发光 (+64)，属性由extra_material_refs记录

            # 阴影 (+32)
            # "has_shadow": False,
            # "shadow_alpha": 0.9,
            # "shadow_angle": -45.0,
            # "shadow_color": "",
            # "shadow_distance": 5.0,
            # "shadow_point": {
            #     "x": 0.6363961030678928,
            #     "y": -0.6363961030678928
            # },
            # "shadow_smoothing": 0.45,

            # 整体字体设置, 似乎会被content覆盖
            # "font_category_id": "",
            # "font_category_name": "",
            # "font_id": "",
            # "font_name": "",
            # "font_path": "",
            # "font_resource_id": "",
            # "font_size": 15.0,
            # "font_source_platform": 0,
            # "font_team_id": "",
            # "font_title": "none",
            # "font_url": "",
            # "fonts": [],

            # 似乎会被content覆盖
            # "text_alpha": 1.0,
            # "text_color": "#FFFFFF",
            # "text_curve": None,
            # "text_preset_resource_id": "",
            # "text_size": 30,
            # "underline": False,
        }

        if self.background:
            ret.update(self.background.export_json())

        return ret
