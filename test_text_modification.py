# 简单测试：验证文本修改是否真的保存到剪映项目中
import pyJianYingDraft as draft
from pyJianYingDraft import trange
from pyJianYingDraft.metadata.text_intro import TextIntro

# 设置草稿文件夹
draft_folder = draft.DraftFolder("./output")

print("=== 测试文本修改保存功能 ===")

# === 第一步：创建原始文本片段 ===
print("\n🔥 第一步：创建原始文本片段")

script = draft_folder.create_draft("test_text_modification", 1920, 1080, allow_replace=True)
script.add_track(draft.TrackType.text)

# 创建文本片段
original_text = draft.TextSegment("Original Text", trange("0s", "3s"))
script.add_segment(original_text)
text_id = original_text.segment_id

print(f"✅ 创建文本片段，ID: {text_id}")
print(f"✅ 原始文本内容: '{original_text.text}'")

# 保存
script.save()
print("✅ 原始草稿已保存")

# === 第二步：加载并修改文本 ===
print("\n🔥 第二步：加载并修改文本")

# 重新加载草稿
script_loaded = draft_folder.load_template("test_text_modification")
print("✅ 草稿已重新加载")

# 通过ID加载文本片段
loaded_text = draft.TextSegment(text_id=text_id, script=script_loaded)
print(f"✅ 加载的文本内容: '{loaded_text.text}'")

# 修改文本内容
loaded_text.text = "Modified Text!"
print(f"✅ 修改后的文本内容: '{loaded_text.text}'")

# 保存修改
script_loaded.save()
print("✅ 修改已保存")

# === 第三步：验证修改是否持久化 ===
print("\n🔥 第三步：验证修改是否持久化")

# 再次重新加载草稿
script_verify = draft_folder.load_template("test_text_modification")
print("✅ 草稿已再次重新加载")

# 再次通过ID加载文本片段
verify_text = draft.TextSegment(text_id=text_id, script=script_verify)
print(f"✅ 验证加载的文本内容: '{verify_text.text}'")

# 检查结果
if verify_text.text == "Modified Text!":
    print("🎉 成功！修改已正确保存到剪映项目中")
else:
    print(f"❌ 失败！期望 'Modified Text!'，但得到 '{verify_text.text}'")

# === 第四步：检查原始script中的数据 ===
print("\n🔥 第四步：检查原始script中的数据")

# 直接检查script中的片段数据
for track in script_loaded.tracks.values():
    for segment in track.segments:
        if segment.segment_id == text_id:
            print(f"✅ Script中的文本内容: '{getattr(segment, 'text', 'NOT_FOUND')}'")
            break

print("\n=== 测试完成 ===")
print("如果修改成功保存，您应该能在剪映中看到 'Modified Text!' 而不是 'Original Text'")
