"""定义音频片段及其相关类

包含淡入淡出效果、音频特效等相关类
"""

import uuid
from copy import deepcopy

from typing import Optional, Literal, Union, TYPE_CHECKING
from typing import Dict, List, Any

if TYPE_CHECKING:
    from .script_file import ScriptFile

from .time_util import tim, Timerange
from .segment import MediaSegment
from .local_materials import AudioMaterial
from .keyframe import KeyframeProperty, KeyframeList

from .metadata import EffectParamInstance
from .metadata import AudioSceneEffectType, ToneEffectType, SpeechToSongType

class AudioFade:
    """音频淡入淡出效果"""

    fade_id: str
    """淡入淡出效果的全局id, 自动生成"""

    in_duration: int
    """淡入时长, 单位为微秒"""
    out_duration: int
    """淡出时长, 单位为微秒"""

    def __init__(self, in_duration: int, out_duration: int):
        """根据给定的淡入/淡出时长构造一个淡入淡出效果"""

        self.fade_id = uuid.uuid4().hex
        self.in_duration = in_duration
        self.out_duration = out_duration

    def export_json(self) -> Dict[str, Any]:
        return {
            "id": self.fade_id,
            "fade_in_duration": self.in_duration,
            "fade_out_duration": self.out_duration,
            "fade_type": 0,
            "type": "audio_fade"
        }

class AudioEffect:
    """音频特效对象"""

    name: str
    """特效名称"""
    effect_id: str
    """特效全局id, 由程序自动生成"""
    resource_id: str
    """资源id, 由剪映本身提供"""

    category_id: Literal["sound_effect", "tone", "speech_to_song"]
    category_name: Literal["场景音", "音色", "声音成曲"]
    category_index: Literal[1, 2, 3]

    audio_adjust_params: List[EffectParamInstance]

    def __init__(self, effect_meta: Union[AudioSceneEffectType, ToneEffectType, SpeechToSongType],
                 params: Optional[List[Optional[float]]] = None):
        """根据给定的音效元数据及参数列表构造一个音频特效对象, params的范围是0~100"""

        self.name = effect_meta.value.name
        self.effect_id = uuid.uuid4().hex
        self.resource_id = effect_meta.value.resource_id
        self.audio_adjust_params = []

        if isinstance(effect_meta, AudioSceneEffectType):
            self.category_id = "sound_effect"
            self.category_name = "场景音"
            self.category_index = 1
        elif isinstance(effect_meta, ToneEffectType):
            self.category_id = "tone"
            self.category_name = "音色"
            self.category_index = 2
        elif isinstance(effect_meta, SpeechToSongType):
            self.category_id = "speech_to_song"
            self.category_name = "声音成曲"
            self.category_index = 3
        else:
            raise TypeError("不支持的元数据类型 %s" % type(effect_meta))

        self.audio_adjust_params = effect_meta.value.parse_params(params)

    def export_json(self) -> Dict[str, Any]:
        return {
            "audio_adjust_params": [param.export_json() for param in self.audio_adjust_params],
            "category_id": self.category_id,
            "category_name": self.category_name,
            "id": self.effect_id,
            "is_ugc": False,
            "name": self.name,
            "production_path": "",
            "resource_id": self.resource_id,
            "speaker_id": "",
            "sub_type": self.category_index,
            "time_range": {"duration": 0, "start": 0},  # 似乎并未用到
            "type": "audio_effect"
            # 不导出path和constant_material_id
        }

class AudioSegment(MediaSegment):
    """安放在轨道上的一个音频片段"""

    material_instance: AudioMaterial
    """音频素材实例"""

    fade: Optional[AudioFade]
    """音频淡入淡出效果, 可能为空

    在放入轨道时自动添加到素材列表中
    """

    effects: List[AudioEffect]
    """音频特效列表

    在放入轨道时自动添加到素材列表中
    """

    def __init__(self, material: Union[AudioMaterial, str] = None, target_timerange: Timerange = None, *,
                 source_timerange: Optional[Timerange] = None, speed: Optional[float] = None, volume: float = 1.0,
                 change_pitch: bool = False, audio_id: Optional[str] = None, script: Optional["ScriptFile"] = None):
        """构建音频片段

        支持两种模式：
        1. 新建模式：AudioSegment(material, target_timerange, ...)
        2. ID加载模式：AudioSegment(audio_id=existing_id, script=script_obj)

        Args:
            material (`AudioMaterial` or `str`, optional): 素材实例或素材路径
            target_timerange (`Timerange`, optional): 片段在轨道上的目标时间范围
            source_timerange (`Timerange`, optional): 截取的素材片段的时间范围
            speed (`float`, optional): 播放速度, 默认为1.0
            volume (`float`, optional): 音量, 默认为1.0
            change_pitch (`bool`, optional): 是否跟随变速改变音调, 默认为否
            audio_id (`str`, optional): 现有片段的ID（ID加载模式）
            script (`ScriptFile`, optional): ScriptFile实例（ID加载模式）

        Raises:
            `ValueError`: 参数错误或指定的source_timerange超出素材时长范围
        """
        if audio_id is not None:
            # ID加载模式：从现有片段完全复制所有数据
            if script is None:
                raise ValueError("ID加载模式下必须提供script参数")
            self._load_from_existing(audio_id, script)
        else:
            # 新建模式：原有的构造逻辑
            if material is None or target_timerange is None:
                raise ValueError("新建模式下必须提供material和target_timerange参数")
            self._init_new_segment(material, target_timerange, source_timerange, speed, volume, change_pitch)

    def _init_new_segment(self, material: Union[AudioMaterial, str], target_timerange: Timerange,
                         source_timerange: Optional[Timerange], speed: Optional[float], volume: float,
                         change_pitch: bool):
        """新建模式的初始化逻辑"""
        if isinstance(material, str):
            material = AudioMaterial(material)

        if source_timerange is not None and speed is not None:
            target_timerange = Timerange(target_timerange.start, round(source_timerange.duration / speed))
        elif source_timerange is not None and speed is None:
            speed = source_timerange.duration / target_timerange.duration
        else:  # source_timerange is None
            speed = speed if speed is not None else 1.0
            source_timerange = Timerange(0, round(target_timerange.duration * speed))

        if source_timerange.end > material.duration:
            raise ValueError(f"截取的素材时间范围 {source_timerange} 超出了素材时长({material.duration})")

        super().__init__(material.material_id, source_timerange, target_timerange, speed, volume, change_pitch)

        self.material_instance = deepcopy(material)
        self.fade = None
        self.effects = []

    def _load_from_existing(self, audio_id: str, script: "ScriptFile"):
        """ID加载模式：从现有片段完全复制所有数据"""
        # 查找现有片段
        existing_segment = script.get_segment_by_id(audio_id)
        if existing_segment is None:
            raise ValueError(f"未找到ID为{audio_id}的片段")

        # 完全复制现有片段的所有属性，确保两个实例完全相同
        if hasattr(existing_segment, 'segment_id'):
            # 普通片段：直接复制所有属性
            self._copy_from_normal_segment(existing_segment)
        else:
            # 导入片段：从raw_data重建
            self._copy_from_imported_segment(existing_segment, script)

    def _copy_from_normal_segment(self, existing_segment):
        """从普通片段复制所有数据"""
        # 复制基础属性
        self.segment_id = existing_segment.segment_id
        self.material_id = existing_segment.material_id
        self.target_timerange = existing_segment.target_timerange
        self.source_timerange = existing_segment.source_timerange
        self.speed = existing_segment.speed
        self.volume = existing_segment.volume
        self.change_pitch = existing_segment.change_pitch

        # 复制素材相关属性
        if hasattr(existing_segment, 'material_instance'):
            self.material_instance = deepcopy(existing_segment.material_instance)

        # 直接引用所有音频效果，确保修改能同步
        self.effects = getattr(existing_segment, 'effects', [])
        self.fade = getattr(existing_segment, 'fade', None)

        # 直接引用额外素材引用
        if hasattr(existing_segment, 'extra_material_refs'):
            self.extra_material_refs = existing_segment.extra_material_refs
        else:
            self.extra_material_refs = []

        # 直接引用关键帧
        if hasattr(existing_segment, 'common_keyframes'):
            self.common_keyframes = existing_segment.common_keyframes
        else:
            self.common_keyframes = []

        # 保存对原始片段的引用
        self._original_segment = existing_segment

        # 复制其他可能的属性
        for attr_name in dir(existing_segment):
            if not attr_name.startswith('_') and not callable(getattr(existing_segment, attr_name)):
                if not hasattr(self, attr_name):
                    try:
                        setattr(self, attr_name, deepcopy(getattr(existing_segment, attr_name)))
                    except:
                        pass  # 忽略无法复制的属性

    def _copy_from_imported_segment(self, existing_segment, script):
        """从导入片段重建所有数据"""
        # 从raw_data获取基础信息
        raw_data = existing_segment.raw_data
        self.segment_id = raw_data.get('id')
        self.material_id = existing_segment.material_id
        self.target_timerange = existing_segment.target_timerange

        # 尝试获取source_timerange
        if hasattr(existing_segment, 'source_timerange'):
            self.source_timerange = existing_segment.source_timerange
        else:
            # 从raw_data重建
            source_data = raw_data.get('source_timerange')
            if source_data:
                self.source_timerange = Timerange(source_data['start'], source_data['duration'])
            else:
                self.source_timerange = self.target_timerange

        # 设置其他基础属性
        self.speed = raw_data.get('speed', 1.0)
        self.volume = raw_data.get('volume', 1.0)
        self.change_pitch = raw_data.get('is_tone_modify', False)

        # 初始化音频效果列表
        self.effects = []
        self.fade = None

        # 复制额外素材引用（深拷贝确保独立性）
        self.extra_material_refs = deepcopy(raw_data.get('extra_material_refs', []))

        # 初始化关键帧列表
        self.common_keyframes = []

        # 重建素材实例（如果可能）
        self._rebuild_material_instance(script)

        # 重建所有音频效果
        self._rebuild_audio_effects_from_materials(script)

    def _rebuild_material_instance(self, script):
        """重建音频素材实例"""
        # 尝试从script的materials中找到对应的素材
        for audio_material in script.materials.audios:
            if audio_material.material_id == self.material_id:
                self.material_instance = deepcopy(audio_material)
                return

        # 如果在普通materials中找不到，尝试在imported_materials中查找
        for audio_data in script.imported_materials.get('audios', []):
            if audio_data.get('id') == self.material_id:
                # 从JSON数据重建AudioMaterial
                # 暂时创建一个基础的实例
                break

    def _rebuild_audio_effects_from_materials(self, script):
        """从materials中重建所有音频效果"""
        # 重建音频特效
        for effect_data in script.imported_materials.get('audio_effects', []):
            if effect_data.get('id') in self.extra_material_refs:
                # 重建音频特效实例
                try:
                    effect_name = effect_data.get('name', '')
                    # 这里可以根据effect_data重建AudioEffect对象
                    # 暂时跳过具体的重建逻辑
                    pass
                except Exception as e:
                    print(f"重建音频特效时出错: {e}")
                    pass

        # 重建音频淡入淡出
        for fade_data in script.imported_materials.get('audio_fades', []):
            if fade_data.get('id') in self.extra_material_refs:
                # 重建音频淡入淡出实例
                try:
                    in_duration = fade_data.get('fade_in_duration', 0)
                    out_duration = fade_data.get('fade_out_duration', 0)
                    fade_inst = AudioFade(in_duration, out_duration)
                    fade_inst.fade_id = fade_data.get('id')
                    self.fade = fade_inst
                    break
                except Exception as e:
                    print(f"重建音频淡入淡出时出错: {e}")
                    pass

    def add_effect(self, effect_type: Union[AudioSceneEffectType, ToneEffectType, SpeechToSongType],
                   params: Optional[List[Optional[float]]] = None) -> "AudioSegment":
        """为音频片段添加一个作用于整个片段的音频效果, 目前"声音成曲"效果不能自动被剪映所识别

        Args:
            effect_type (`AudioSceneEffectType` | `ToneEffectType` | `SpeechToSongType`): 音效类型, 一类音效只能添加一个.
            params (`List[Optional[float]]`, optional): 音效参数列表, 参数列表中未提供或为None的项使用默认值.
                参数取值范围(0~100)与剪映中一致. 某个特效类型有何参数以及具体参数顺序以枚举类成员的annotation为准.

        Raises:
            `ValueError`: 试图添加一个已经存在的音效类型、提供的参数数量超过了该音效类型的参数数量, 或参数值超出范围.
        """
        if params is not None and len(params) > len(effect_type.value.params):
            raise ValueError("为音频效果 %s 传入了过多的参数" % effect_type.value.name)

        effect_inst = AudioEffect(effect_type, params)
        if effect_inst.category_id in [eff.category_id for eff in self.effects]:
            raise ValueError("当前音频片段已经有此类型 (%s) 的音效了" % effect_inst.category_name)
        self.effects.append(effect_inst)
        self.extra_material_refs.append(effect_inst.effect_id)

        return self

    def add_fade(self, in_duration: Union[str, int], out_duration: Union[str, int]) -> "AudioSegment":
        """为音频片段添加淡入淡出效果

        Args:
            in_duration (`int` or `str`): 音频淡入时长, 单位为微秒, 若为字符串则会调用`tim()`函数进行解析
            out_duration (`int` or `str`): 音频淡出时长, 单位为微秒, 若为字符串则会调用`tim()`函数进行解析

        Raises:
            `ValueError`: 当前片段已存在淡入淡出效果
        """
        if self.fade is not None:
            raise ValueError("当前片段已存在淡入淡出效果")

        if isinstance(in_duration, str): in_duration = tim(in_duration)
        if isinstance(out_duration, str): out_duration = tim(out_duration)

        self.fade = AudioFade(in_duration, out_duration)
        self.extra_material_refs.append(self.fade.fade_id)

        return self

    def add_keyframe(self, time_offset: int, volume: float) -> "AudioSegment":
        """为音频片段创建一个*控制音量*的关键帧, 并自动加入到关键帧列表中

        Args:
            time_offset (`int`): 关键帧的时间偏移量, 单位为微秒
            volume (`float`): 音量在`time_offset`处的值
        """
        _property = KeyframeProperty.volume
        for kf_list in self.common_keyframes:
            if kf_list.keyframe_property == _property:
                kf_list.add_keyframe(time_offset, volume)
                return self
        kf_list = KeyframeList(_property)
        kf_list.add_keyframe(time_offset, volume)
        self.common_keyframes.append(kf_list)
        return self

    def export_json(self) -> Dict[str, Any]:
        json_dict = super().export_json()
        json_dict.update({
            "clip": None,
            "hdr_settings": None
        })
        return json_dict
