import pyJianYingDraft as draft
from pyJianYingDraft import trange
from pyJianYingDraft.metadata.video_outro import OutroType

draft_folder = draft.DraftFolder('./output')
video_path = r'D:\pythonProject\MyProject\pyJianYingDraft\readme_assets\tutorial\video.mp4'

script = draft_folder.create_draft('debug_video_fix', 1920, 1080, allow_replace=True)
script.add_track(draft.TrackType.video)

video1 = draft.VideoSegment(video_path, trange('0s', '3s'))
video1_id = video1.segment_id
script.add_segment(video1)
script.save()

script = draft_folder.load_template('debug_video_fix')
video1_loaded = draft.VideoSegment(video_id=video1_id, script=script)
print(f'有_original_segment: {hasattr(video1_loaded, "_original_segment")}')
print(f'_original_segment不为None: {getattr(video1_loaded, "_original_segment", None) is not None}')

video1_loaded.add_animation(OutroType.渐隐)
script.save()

script_verify = draft_folder.load_template('debug_video_fix')
video1_verify = draft.VideoSegment(video_id=video1_id, script=script_verify)
print(f'验证动画实例: {video1_verify.animations_instance is not None}')
if video1_verify.animations_instance:
    print(f'验证动画数量: {len(video1_verify.animations_instance.animations)}')
