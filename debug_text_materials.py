# 调试：检查文本素材的数据结构
import pyJianYingDraft as draft
from pyJianYingDraft import trange
import json

# 设置草稿文件夹
draft_folder = draft.DraftFolder("./output")

print("=== 调试文本素材数据结构 ===")

# 创建文本片段
script = draft_folder.create_draft("debug_text_materials", 1920, 1080, allow_replace=True)
script.add_track(draft.TrackType.text)

original_text = draft.TextSegment("Debug Text Content", trange("0s", "3s"))
script.add_segment(original_text)
text_id = original_text.segment_id
material_id = original_text.material_id

print(f"✅ 创建文本片段，ID: {text_id}")
print(f"✅ 文本内容: '{original_text.text}'")
print(f"✅ 素材ID: {material_id}")

# 保存
script.save()
print("✅ 草稿已保存")

# 重新加载并检查materials
script_loaded = draft_folder.load_template("debug_text_materials")
print("✅ 草稿已重新加载")

# 检查imported_materials
print(f"✅ imported_materials keys: {script_loaded.imported_materials.keys()}")

# 查找文本素材
if 'texts' in script_loaded.imported_materials:
    print(f"✅ texts数量: {len(script_loaded.imported_materials['texts'])}")
    for i, text_material in enumerate(script_loaded.imported_materials['texts']):
        print(f"  文本素材 {i}:")
        print(f"    ID: {text_material.get('id', 'N/A')}")
        print(f"    内容: {json.dumps(text_material, indent=4, ensure_ascii=False)}")
        if text_material.get('id') == material_id:
            print(f"    ✅ 找到匹配的文本素材！")

# 查找片段
found_segment = script_loaded.get_segment_by_id(text_id)
print(f"✅ 找到的片段material_id: {getattr(found_segment, 'material_id', 'N/A')}")

print("\n=== 调试完成 ===")
