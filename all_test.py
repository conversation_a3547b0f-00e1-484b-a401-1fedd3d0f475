import pyJianYingDraft as draft
from pyJianYingDraft import trange
from pyJianYingDraft.metadata.video_outro import OutroType

draft_folder = draft.DraftFolder(r"D:\software\JianyingPro Drafts")
video_path = r"D:\pythonProject\MyProject\pyJianYingDraft\readme_assets\tutorial\video.mp4"

script = draft_folder.create_draft("debug_video", 1920, 1080, allow_replace=True)
script.add_track(draft.TrackType.video)

video1 = draft.VideoSegment(video_path, trange("0s", "3s"))
video1_id = video1.segment_id
script.add_segment(video1)
script.save()

print(f"原始动画实例: {video1.animations_instance}")

script = draft_folder.load_template("debug_video")
video1_loaded = draft.VideoSegment(video_id=video1_id, script=script)
print(f"加载后动画实例: {video1_loaded.animations_instance}")
print(f"有_original_segment: {hasattr(video1_loaded, '_original_segment')}")

video1_loaded.add_animation(OutroType.渐隐)
print(f"添加动画后: {video1_loaded.animations_instance}")
if video1_loaded.animations_instance:
    print(f"动画数量: {len(video1_loaded.animations_instance.animations)}")

script.save()

# 验证是否保存成功
script_verify = draft_folder.load_template("debug_video")
video1_verify = draft.VideoSegment(video_id=video1_id, script=script_verify)
print(f"验证动画实例: {video1_verify.animations_instance}")
if video1_verify.animations_instance:
    print(f"验证动画数量: {len(video1_verify.animations_instance.animations)}")