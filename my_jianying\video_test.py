# -*- coding: utf-8 -*-
"""
Author: ji<PERSON> <PERSON><PERSON>reate Time: 2025/8/8 上午10:12
File Name:video_test.py.py
"""
import os
import pyJianYingDraft as draft
from pyJianYingDraft import IntroType, TransitionType, trange, tim
draft_folder = draft.DraftFolder("./output")

tutorial_asset_dir = os.path.join(os.path.dirname(__file__), 'readme_assets', 'tutorial')
assert os.path.exists(tutorial_asset_dir), f"未找到例程素材文件夹{os.path.abspath(tutorial_asset_dir)}"
# 创建剪映草稿
script = draft_folder.create_draft("video_test", 1920, 1080)  # 1920x1080分辨率
script.add_track(draft.TrackType.video)
# 创建视频片段（使用便捷构造，直接传入素材路径）
video_segment = draft.VideoSegment(os.path.join(tutorial_asset_dir, 'video.mp4'),
                                   trange("0s", "4.2s"))
script.save()