# 测试同一轨道两个视频+转场效果
import os
import pyJianYingDraft as draft
from pyJianYingDraft import IntroType, OutroType, TransitionType, FilterType, trange

# 设置草稿文件夹
draft_folder = draft.DraftFolder("./output")

# 视频素材路径
video_path = r"D:\pythonProject\MyProject\pyJianYingDraft\readme_assets\tutorial\video.mp4"
assert os.path.exists(video_path), f"未找到视频文件: {video_path}"

print("=== 测试同一轨道两个视频+转场效果 ===")

# === 第一阶段：创建两个视频片段 ===
print("\n🔥 第一阶段：创建两个视频片段")

script = draft_folder.create_draft("test_two_videos_transition", 1920, 1080, allow_replace=True)
script.add_track(draft.TrackType.video)

# 创建第一个视频片段
print("📹 创建第一个视频片段...")
video1 = draft.VideoSegment(video_path, trange("0s", "2.5s"))
video1.add_animation(IntroType.斜切)
video1.add_filter(FilterType.亮肤, 75.0)
# 为第一个视频添加转场（转场应该添加在前面的片段上）
video1.add_transition(TransitionType.信号故障)
script.add_segment(video1)
video1_id = video1.segment_id

print(f"✅ 第一个视频ID: {video1_id}")
print(f"✅ 第一个视频转场: {video1.transition is not None}")

# 创建第二个视频片段
print("📹 创建第二个视频片段...")
video2 = draft.VideoSegment(video_path, trange("2.5s", "5s"))
video2.add_animation(OutroType.渐隐)
video2.add_filter(FilterType.冷白, 50.0)
script.add_segment(video2)
video2_id = video2.segment_id

print(f"✅ 第二个视频ID: {video2_id}")
print(f"✅ 第二个视频动画: {video2.animations_instance is not None}")

script.save()
print("✅ 草稿已保存")

# === 第二阶段：通过ID加载并修改第一个视频 ===
print("\n🔥 第二阶段：通过ID加载并修改第一个视频")

script_loaded = draft_folder.load_template("test_two_videos_transition")
print("✅ 草稿已重新加载")

# 通过ID加载第一个视频
loaded_video1 = draft.VideoSegment(video_id=video1_id, script=script_loaded)
print(f"📹 加载第一个视频: {loaded_video1.segment_id}")

# 验证原有数据
print("📊 第一个视频数据验证:")
print(f"  动画实例: {loaded_video1.animations_instance is not None}")
print(f"  滤镜数量: {len(loaded_video1.filters)}")
print(f"  转场: {loaded_video1.transition is not None}")
print(f"  额外素材引用: {len(loaded_video1.extra_material_refs)}")

# 在第一个视频上添加新效果
loaded_video1.add_filter(FilterType.入夏, 80.0)  # 添加第二个滤镜
loaded_video1.add_animation(OutroType.缩小)  # 添加出场动画

print("✅ 为第一个视频添加了第二个滤镜和出场动画")
print(f"📊 修改后: 滤镜={len(loaded_video1.filters)}, 额外引用={len(loaded_video1.extra_material_refs)}")

# === 第三阶段：通过ID加载并修改第二个视频 ===
print("\n🔥 第三阶段：通过ID加载并修改第二个视频")

# 通过ID加载第二个视频
loaded_video2 = draft.VideoSegment(video_id=video2_id, script=script_loaded)
print(f"📹 加载第二个视频: {loaded_video2.segment_id}")

# 验证原有数据
print("📊 第二个视频数据验证:")
print(f"  动画实例: {loaded_video2.animations_instance is not None}")
print(f"  滤镜数量: {len(loaded_video2.filters)}")
print(f"  转场: {loaded_video2.transition is not None}")
print(f"  额外素材引用: {len(loaded_video2.extra_material_refs)}")

# 在第二个视频上添加新效果
loaded_video2.add_filter(FilterType.书意, 60.0)  # 添加滤镜
loaded_video2.add_animation(IntroType.放大)  # 添加入场动画
loaded_video2.add_transition(TransitionType.闪白)  # 添加转场

print("✅ 为第二个视频添加了滤镜、入场动画和转场")
print(f"📊 修改后: 滤镜={len(loaded_video2.filters)}, 转场={loaded_video2.transition is not None}")

# === 第四阶段：验证两个视频的独立性 ===
print("\n🔥 第四阶段：验证两个视频的独立性")

print("📊 两个视频的最终状态:")
print(f"  视频1 - 滤镜数量: {len(loaded_video1.filters)}")
print(f"  视频1 - 动画实例: {loaded_video1.animations_instance is not None}")
print(f"  视频1 - 转场: {loaded_video1.transition is not None}")
print(f"  视频1 - 额外素材引用: {len(loaded_video1.extra_material_refs)}")

print(f"  视频2 - 滤镜数量: {len(loaded_video2.filters)}")
print(f"  视频2 - 动画实例: {loaded_video2.animations_instance is not None}")
print(f"  视频2 - 转场: {loaded_video2.transition is not None}")
print(f"  视频2 - 额外素材引用: {len(loaded_video2.extra_material_refs)}")

# === 第五阶段：测试链式调用 ===
print("\n🔥 第五阶段：测试链式调用")

# 重新加载第一个视频并进行链式调用
video1_chain = draft.VideoSegment(video_id=video1_id, script=script_loaded)
result = (video1_chain
          .add_filter(FilterType.克洛伊, 90.0)
          .add_animation(IntroType.动感放大))

print("✅ 第一个视频链式调用成功")
print(f"  最终滤镜数量: {len(video1_chain.filters)}")

# 重新加载第二个视频并进行链式调用
video2_chain = draft.VideoSegment(video_id=video2_id, script=script_loaded)
result2 = (video2_chain
           .add_filter(FilterType.元气新年, 70.0)
           .add_animation(OutroType.旋转))

print("✅ 第二个视频链式调用成功")
print(f"  最终滤镜数量: {len(video2_chain.filters)}")

print("\n🎉 测试完成！")

print("\n=== 功能验证总结 ===")
print("✅ 成功在同一轨道创建两个视频片段")
print("✅ 第一个视频包含转场效果（用于与第二个视频的过渡）")
print("✅ 通过ID成功加载两个不同的视频片段")
print("✅ 每个视频的数据完全独立，互不影响")
print("✅ 所有add_*方法在两个视频上都正常工作")
print("✅ 支持对不同视频进行不同的效果组合")
print("✅ 链式调用在两个视频上都正常工作")

print("\n=== 使用场景演示 ===")
print("这个测试演示了典型的视频编辑场景：")
print("1. 创建多个视频片段组成完整时间线")
print("2. 在片段之间添加转场效果")
print("3. 保存项目后可以随时重新加载")
print("4. 通过ID精确定位和修改特定片段")
print("5. 每个片段可以独立添加不同的效果组合")
