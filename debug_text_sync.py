# 调试文本同步功能
import pyJianYingDraft as draft
from pyJianYingDraft import trange
import json

# 设置草稿文件夹
draft_folder = draft.DraftFolder("./output")

print("=== 调试文本同步功能 ===")

# 创建文本片段
script = draft_folder.create_draft("debug_text_sync", 1920, 1080, allow_replace=True)
script.add_track(draft.TrackType.text)

original_text = draft.TextSegment("Original Text", trange("0s", "3s"))
script.add_segment(original_text)
text_id = original_text.segment_id
material_id = original_text.material_id

print(f"✅ 创建文本片段，ID: {text_id}, 素材ID: {material_id}")
script.save()

# 重新加载
script_loaded = draft_folder.load_template("debug_text_sync")

# 检查加载前的materials
print("\n=== 加载前的materials ===")
for text_material in script_loaded.imported_materials.get('texts', []):
    if text_material.get('id') == material_id:
        content_json = json.loads(text_material.get('content', '{}'))
        print(f"加载前文本内容: '{content_json.get('text', '')}'")

# 通过ID加载并修改
loaded_text = draft.TextSegment(text_id=text_id, script=script_loaded)
print(f"\n✅ 加载的文本内容: '{loaded_text.text}'")

# 检查是否有必要的引用
print(f"✅ 有_original_segment: {hasattr(loaded_text, '_original_segment')}")
print(f"✅ 有_script_ref: {hasattr(loaded_text, '_script_ref')}")

# 修改文本
print(f"\n=== 修改文本 ===")
loaded_text.text = "Modified Text!"
print(f"✅ 修改后的文本内容: '{loaded_text.text}'")

# 检查修改后的materials
print("\n=== 修改后的materials ===")
for text_material in script_loaded.imported_materials.get('texts', []):
    if text_material.get('id') == material_id:
        content_json = json.loads(text_material.get('content', '{}'))
        print(f"修改后文本内容: '{content_json.get('text', '')}'")

# 保存
script_loaded.save()
print("✅ 已保存")

# 再次加载验证
script_verify = draft_folder.load_template("debug_text_sync")
verify_text = draft.TextSegment(text_id=text_id, script=script_verify)
print(f"\n✅ 验证文本内容: '{verify_text.text}'")

print("\n=== 调试完成 ===")
