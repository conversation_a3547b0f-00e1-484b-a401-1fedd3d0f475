# 调试：检查文本片段的实际数据结构
import pyJianYingDraft as draft
from pyJianYingDraft import trange
import json

# 设置草稿文件夹
draft_folder = draft.DraftFolder("./output")

print("=== 调试文本片段数据结构 ===")

# 创建文本片段
script = draft_folder.create_draft("debug_text_data", 1920, 1080, allow_replace=True)
script.add_track(draft.TrackType.text)

original_text = draft.TextSegment("Debug Text Content", trange("0s", "3s"))
script.add_segment(original_text)
text_id = original_text.segment_id

print(f"✅ 创建文本片段，ID: {text_id}")
print(f"✅ 原始文本内容: '{original_text.text}'")

# 保存
script.save()
print("✅ 草稿已保存")

# 重新加载并检查数据结构
script_loaded = draft_folder.load_template("debug_text_data")
print("✅ 草稿已重新加载")

# 查找片段
found_segment = script_loaded.get_segment_by_id(text_id)
print(f"✅ 找到的片段类型: {type(found_segment)}")

if found_segment:
    print(f"✅ 片段属性:")
    for attr in dir(found_segment):
        if not attr.startswith('_'):
            try:
                value = getattr(found_segment, attr)
                if not callable(value):
                    print(f"  {attr}: {value}")
            except:
                pass
    
    if hasattr(found_segment, 'raw_data'):
        print(f"✅ raw_data内容:")
        print(json.dumps(found_segment.raw_data, indent=2, ensure_ascii=False))
    
    # 尝试通过ID加载
    print("\n=== 尝试通过ID加载 ===")
    try:
        loaded_text = draft.TextSegment(text_id=text_id, script=script_loaded)
        print(f"✅ 加载成功，文本内容: '{loaded_text.text}'")
        
        # 检查加载后的属性
        print(f"✅ 加载后的属性:")
        print(f"  text: '{loaded_text.text}'")
        print(f"  segment_id: {loaded_text.segment_id}")
        print(f"  target_timerange: {loaded_text.target_timerange}")
        
    except Exception as e:
        print(f"❌ 加载失败: {e}")
        import traceback
        traceback.print_exc()

print("\n=== 调试完成 ===")
